package za.co.wethinkcode.robots.server;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Robot;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to debug obstacle world capacity issues
 */
public class ObstacleWorldTest {
    
    private World world;
    
    @BeforeEach
    void setUp() {
        world = new World(2, 2); // 2x2 world = 3x3 coordinate system
        world.clearWorld(); // Clear any existing state
        
        // Add obstacle at [1,1]
        Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, 1, 1, 1, 1);
        boolean added = world.addObstacle(obstacle);
        assertTrue(added, "Obstacle should be added successfully");
    }
    
    @Test
    void testWorldDimensions() {
        assertEquals(2, world.getWidth());
        assertEquals(2, world.getHeight());
        assertEquals(1, world.getHalfWidth());
        assertEquals(1, world.getHalfHeight());
    }
    
    @Test
    void testObstacleAdded() {
        assertEquals(1, world.getObstacles().size());
        Obstacle obstacle = world.getObstacles().get(0);
        assertEquals(1, obstacle.getX());
        assertEquals(1, obstacle.getY());
    }
    
    @Test
    void testWorldCapacityWithObstacle() {
        // 2x2 world should have 3x3 = 9 total positions
        // With 1 obstacle at [1,1], should have 8 available positions
        
        // Try to add 8 robots - should all succeed
        for (int i = 0; i < 8; i++) {
            Robot robot = new Robot("Robot" + i);
            Status result = world.addRobot(robot);
            assertEquals(Status.OK, result, "Robot " + i + " should be added successfully");
        }
        
        assertEquals(8, world.getRobots().size(), "Should have 8 robots in world");
        
        // Try to add 9th robot - should fail
        Robot ninthRobot = new Robot("Robot9");
        Status result = world.addRobot(ninthRobot);
        assertEquals(Status.WORLDFULL, result, "9th robot should be rejected - world is full");
        
        assertEquals(8, world.getRobots().size(), "Should still have only 8 robots in world");
    }
    
    @Test
    void testRobotPositions() {
        // Add a few robots and verify none are placed at obstacle position [1,1]
        for (int i = 0; i < 5; i++) {
            Robot robot = new Robot("TestRobot" + i);
            Status result = world.addRobot(robot);
            assertEquals(Status.OK, result);
            
            // Verify robot is not at obstacle position
            assertFalse(robot.getX() == 1 && robot.getY() == 1, 
                "Robot should not be placed at obstacle position [1,1]");
        }
    }
    
    @Test
    void testAvailablePositions() {
        // In a 2x2 world (3x3 coordinate system), positions are:
        // [-1,-1] [-1,0] [-1,1]
        // [0,-1]  [0,0]  [0,1]
        // [1,-1]  [1,0]  [1,1] <- obstacle here
        
        // So available positions should be 8: all except [1,1]
        
        Robot robot1 = new Robot("R1");
        world.addRobot(robot1);
        System.out.println("Robot1 at: [" + robot1.getX() + "," + robot1.getY() + "]");
        
        Robot robot2 = new Robot("R2");
        world.addRobot(robot2);
        System.out.println("Robot2 at: [" + robot2.getX() + "," + robot2.getY() + "]");
        
        // Print world state for debugging
        System.out.println("World state:");
        System.out.println(world.getFullWorldState());
    }
}
