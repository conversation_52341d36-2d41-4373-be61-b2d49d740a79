package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;

import static org.junit.jupiter.api.Assertions.*;

public class MoveForwardTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer(){
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        serverClient.disconnect();
    }

    @Test
    void moveForwardAtTheEdgeOfTheWorld(){
        //Given that I am connected to a running Robot world server,
        //And the world is of size 1x1 with no obstacles or pits
        assertTrue(serverClient.isConnected());

        //And a robot called "<PERSON><PERSON>" is already connected and launched
        String launchRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"sniper\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(launchRequest);
        assertEquals("OK", response.get("result").asText());

        //When I send a command for 'HAL' to move forward 5 steps
        String moveRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"forward\"," +
                "\"arguments\": [\"5\"]" +
                "}";
        JsonNode moveResponse = serverClient.sendRequest(moveRequest);

        //Then I should get an 'OK' response with the message 'At the NORTH edge'
        assertEquals("OK", moveResponse.get("result").asText());
        assertTrue(moveResponse.get("data").get("message").asText().contains("At the NORTH edge"));

        //And the position information should be at co-ordinates[0,0]
        assertEquals(0,response.get("data").get("position").get(0).asInt());
        assertEquals(0,response.get("data").get("position").get(1).asInt());

    }
}
