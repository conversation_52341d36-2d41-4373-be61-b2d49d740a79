package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test that launches 10 robots on the reference server with a 2x2 world.
 * This test verifies that the reference server can handle multiple robot launches
 * and that each robot gets a unique position in the world.
 * 
 * Note: A 2x2 world creates a 3x3 coordinate system (from -1 to 1) with 9 total positions.
 */
public class Launch10RobotsReferenceServerTest {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private final ConfigLoader configLoader = new ConfigLoader();
    private Process serverProcess;

    @BeforeEach
    void startReferenceServerAndConnect() throws IOException {
        // Start reference server with 2x2 world (9 robot capacity: 3x3 coordinate system from -1 to 1)
        startReferenceServer("-s", "2");

        // Wait for server to start
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectAndStopServer() {
        try {
            if (serverClient.isConnected()) {
                serverClient.disconnect();
            }
        } catch (RuntimeException e) {
            // Server may have already disconnected - this is acceptable
            System.out.println("Server already disconnected: " + e.getMessage());
        }

        // Kill server process
        if (serverProcess != null && serverProcess.isAlive()) {
            serverProcess.destroyForcibly();
            try {
                serverProcess.waitFor();
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    private void startReferenceServer(String... args) throws IOException {
        // Use the latest reference server JAR
        String referenceServerJar = ".libs/reference-server-0.2.3.jar";
        
        ProcessBuilder pb = new ProcessBuilder();
        pb.command().add("java");
        pb.command().add("-jar");
        pb.command().add(referenceServerJar);
        
        // Add any additional arguments (like -s 2)
        for (String arg : args) {
            pb.command().add(arg);
        }
        
        serverProcess = pb.start();
        System.out.println("Started reference server with args: " + String.join(" ", args));
    }

    @Test
    @DisplayName("Launch 10 robots on reference server with 2x2 world - expect 9 to succeed, 1 to fail")
    void launch10RobotsOnReferenceServer2x2World() {
        // Given that I am connected to the reference server
        // And the world is 2x2 (9 robot capacity: coordinates from -1 to 1)
        assertTrue(serverClient.isConnected());
        System.out.println("Connected to reference server with 2x2 world");

        int successfulLaunches = 0;
        int failedLaunches = 0;

        // When I attempt to launch 10 robots with unique names
        for (int i = 1; i <= 10; i++) {
            String robotName = "Robot" + i;
            String launchRequest = "{" +
                    "\"robot\": \"" + robotName + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            
            System.out.println("Attempting to launch robot: " + robotName);
            JsonNode response = serverClient.sendRequest(launchRequest);
            
            // Check if launch was successful
            assertNotNull(response.get("result"), "Response should have a result field for " + robotName);
            
            if ("OK".equals(response.get("result").asText())) {
                successfulLaunches++;
                
                // Verify response has data with position
                assertNotNull(response.get("data"), "Response should have data field for " + robotName);
                assertNotNull(response.get("data").get("position"), 
                        "Response should have position data for " + robotName);
                
                // Get and display robot position
                JsonNode position = response.get("data").get("position");
                int x = position.get(0).asInt();
                int y = position.get(1).asInt();
                System.out.println("Robot " + robotName + " launched successfully at position [" + x + "," + y + "]");
                
                // Verify position is within 2x2 world bounds (coordinates from -1 to 1)
                assertTrue(x >= -1 && x <= 1, "X coordinate should be within 2x2 world bounds for " + robotName);
                assertTrue(y >= -1 && y <= 1, "Y coordinate should be within 2x2 world bounds for " + robotName);
                
            } else {
                failedLaunches++;
                System.out.println("Robot " + robotName + " failed to launch: " + 
                    response.get("data").get("message").asText());
            }
        }
        
        // Then exactly 9 robots should launch successfully (2x2 world capacity)
        assertEquals(9, successfulLaunches, 
                "Should have launched exactly 9 robots in 2x2 world, but launched " + successfulLaunches);
        
        // And exactly 1 robot should fail to launch (world full)
        assertEquals(1, failedLaunches, 
                "Should have 1 failed launch due to world being full, but had " + failedLaunches + " failures");
        
        System.out.println("Test completed successfully!");
        System.out.println("Successful launches: " + successfulLaunches);
        System.out.println("Failed launches: " + failedLaunches);
    }

    @Test
    @DisplayName("Verify all 9 positions are occupied after launching 9 robots")
    void verifyAllPositionsOccupiedIn2x2World() {
        // Given that I am connected to the reference server
        assertTrue(serverClient.isConnected());
        
        // When I launch 9 robots (filling the 2x2 world completely)
        for (int i = 1; i <= 9; i++) {
            String robotName = "TestRobot" + i;
            String launchRequest = "{" +
                    "\"robot\": \"" + robotName + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"tank\",\"5\",\"5\"]" +
                    "}";
            
            JsonNode response = serverClient.sendRequest(launchRequest);
            assertEquals("OK", response.get("result").asText(), 
                    "Robot " + robotName + " should launch successfully");
        }
        
        // Then when I try to launch one more robot, it should fail
        String extraRobotRequest = "{" +
                "\"robot\": \"ExtraRobot\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"sniper\",\"5\",\"5\"]" +
                "}";
        
        JsonNode response = serverClient.sendRequest(extraRobotRequest);
        
        // The launch should fail because the world is full
        assertEquals("ERROR", response.get("result").asText(), 
                "Extra robot should fail to launch because 2x2 world is full");
        
        // Verify the error message indicates the world is full
        String errorMessage = response.get("data").get("message").asText().toLowerCase();
        assertTrue(errorMessage.contains("full") || errorMessage.contains("capacity") ||
                   errorMessage.contains("no space") || errorMessage.contains("no more space"),
                "Error message should indicate world is full: " + errorMessage);
        
        System.out.println("Successfully verified that 2x2 world is full after 9 robot launches");
    }
}
