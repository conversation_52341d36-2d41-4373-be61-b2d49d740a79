# Launch 10 Robots Reference Server Test

This test verifies that the reference server can successfully handle launching 10 robots and provides insights into the server's robot capacity management.

## 📋 Test Overview

The `Launch10RobotsReferenceServerTest` class contains two main test methods:

1. **`launch10RobotsOnReferenceServer()`** - Launches exactly 10 robots and verifies each launch succeeds
2. **`verifyWorldCapacityAfterLaunching10Robots()`** - Tests world capacity limits by launching robots until the world is full

## 🚀 How to Run

### Option 1: Using the Batch Script (Windows)
```bash
run-10-robots-test.bat
```

### Option 2: Using PowerShell (Cross-platform)
```powershell
# Windows
powershell -ExecutionPolicy Bypass -File .\run-10-robots-test.ps1

# Linux/Mac
pwsh ./run-10-robots-test.ps1
```

### Option 3: Using Maven Directly
```bash
# Build first
mvn clean package -DskipTests

# Run the test
mvn test -Dtest=Launch10RobotsReferenceServerTest
```

### Option 4: Run Individual Test Methods
```bash
# Run only the 10 robots launch test
mvn test -Dtest=Launch10RobotsReferenceServerTest#launch10RobotsOnReferenceServer

# Run only the capacity test
mvn test -Dtest=Launch10RobotsReferenceServerTest#verifyWorldCapacityAfterLaunching10Robots
```

## 🔧 Test Configuration

The test automatically:
- **Starts the reference server** using `.libs/reference-server-0.2.3.jar`
- **Configures a 4x4 world** (25 robot capacity) using `-s 4` argument
- **Connects to localhost:5000**
- **Launches robots with unique names** (Robot1, Robot2, etc.)
- **Cleans up the server process** after each test

## 📊 Expected Results

### Test 1: `launch10RobotsOnReferenceServer()`
- ✅ All 10 robots should launch successfully
- ✅ Each robot gets a unique position in the world
- ✅ All positions are within world bounds (-2 to 2 for 4x4 world)
- ✅ Server responds with "OK" status for each launch

### Test 2: `verifyWorldCapacityAfterLaunching10Robots()`
- ✅ First 10 robots launch successfully
- ✅ Additional robots launch until world capacity is reached
- ✅ World becomes full at 25 robots (4x4 = 25 positions)
- ✅ Server responds with appropriate error when world is full

## 🐛 Troubleshooting

### Common Issues

1. **Reference server JAR not found**
   ```
   Error: Could not find .libs/reference-server-0.2.3.jar
   ```
   **Solution**: Ensure the reference server JAR exists in the `.libs/` directory

2. **Port already in use**
   ```
   java.net.BindException: Address already in use
   ```
   **Solution**: Kill any existing server processes on port 5000:
   ```bash
   # Windows
   netstat -ano | findstr :5000
   taskkill /PID <PID> /F
   
   # Linux/Mac
   lsof -ti:5000 | xargs kill -9
   ```

3. **Test timeout**
   ```
   java.net.SocketTimeoutException: Read timed out
   ```
   **Solution**: The server might be taking longer to start. The test waits 2 seconds - you can increase this in the `@BeforeEach` method.

4. **Connection refused**
   ```
   java.net.ConnectException: Connection refused
   ```
   **Solution**: Ensure the reference server JAR is compatible with your Java version and starts correctly.

## 📝 Test Output Example

```
Connected to reference server
Launching robot: Robot1
Robot Robot1 launched at position [0,0]
Launching robot: Robot2
Robot Robot2 launched at position [1,0]
...
Launching robot: Robot10
Robot Robot10 launched at position [-1,2]
Successfully launched all 10 robots on reference server!
```

## 🔍 What This Test Validates

- **Reference server functionality** - Verifies the reference server works correctly
- **Multiple robot handling** - Tests concurrent robot management
- **Position allocation** - Ensures robots get unique, valid positions
- **World capacity** - Validates world size limits and error handling
- **JSON communication** - Tests proper request/response format
- **Server stability** - Ensures server remains stable under load

## 📁 Files Created

- `src/test/java/za/co/wethinkcode/robots/AcceptanceTests/Launch10RobotsReferenceServerTest.java` - Main test class
- `run-10-robots-test.bat` - Windows batch script
- `run-10-robots-test.ps1` - PowerShell script (cross-platform)
- `LAUNCH_10_ROBOTS_TEST_README.md` - This documentation

## 🎯 Next Steps

After running this test successfully, you can:
1. **Modify the world size** by changing the `-s` parameter in `startReferenceServer()`
2. **Test with obstacles** by adding `-o x,y` parameters
3. **Test different robot types** by modifying the launch requests
4. **Add performance measurements** to track launch times
5. **Test error scenarios** like duplicate names or invalid commands
