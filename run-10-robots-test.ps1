#!/usr/bin/env pwsh

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Running 10 Robots Reference Server Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "Building project..." -ForegroundColor Yellow
& mvn clean package -DskipTests
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Running Launch10RobotsReferenceServerTest..." -ForegroundColor Yellow
& mvn test -Dtest=Launch10RobotsReferenceServerTest

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green

# Keep window open if running in Windows
if ($IsWindows -or $env:OS -eq "Windows_NT") {
    Read-Host "Press Enter to continue..."
}
